<Window x:Class="FluentDesignWPF.Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FluentDesignWPF.Demo"
        mc:Ignorable="d"
        Title="FluentDesignWPF 色彩系统演示"
        Height="700"
        Width="1200"
        Background="White"
        Foreground="Black">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题和主题切换 -->
        <Grid Grid.Row="0" Margin="{StaticResource MarginMedium}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="FluentDesignWPF 色彩系统演示"
                       Style="{StaticResource FluentTitleTextBlock}"/>

            <Button Grid.Column="1"
                    x:Name="ThemeToggleButton"
                    Content="切换主题"
                    Padding="{StaticResource PaddingMedium}"
                    Click="ThemeToggleButton_Click"/>
        </Grid>

        <!-- 说明文本 -->
        <TextBlock Grid.Row="1"
                   Text="此演示展示了FluentDesignWPF控件库的完整色彩系统，包括主题色、中性色、语义色和状态色。点击上方按钮可以在深色和浅色主题之间切换。"
                   Style="{StaticResource FluentSubtitleTextBlock}"
                   Margin="{StaticResource MarginMedium}"/>

        <!-- 色彩展示区域 -->
        <ScrollViewer Grid.Row="2"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="{StaticResource MarginMedium}">

                <!-- 主题色彩 -->
                <TextBlock Text="主题色彩 (Primary Colors)"
                           Style="{StaticResource FluentTitleTextBlock}"
                           Margin="0,0,0,16"/>

                <UniformGrid Columns="5" Margin="0,0,0,32">
                    <Border Background="{StaticResource PrimaryBrush100}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Primary 100"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource TextPrimaryLightBrush}"/>
                    </Border>
                    <Border Background="{StaticResource PrimaryBrush300}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Primary 300"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource WhiteBrush}"/>
                    </Border>
                    <Border Background="{StaticResource PrimaryBrush500}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Primary 500"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource WhiteBrush}"/>
                    </Border>
                    <Border Background="{StaticResource PrimaryBrush700}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Primary 700"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource WhiteBrush}"/>
                    </Border>
                    <Border Background="{StaticResource PrimaryBrush900}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Primary 900"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource WhiteBrush}"/>
                    </Border>
                </UniformGrid>

                <!-- 语义色彩 -->
                <TextBlock Text="语义色彩 (Semantic Colors)"
                           Style="{StaticResource FluentTitleTextBlock}"
                           Margin="0,0,0,16"/>

                <UniformGrid Columns="4" Margin="0,0,0,32">
                    <Border Background="{StaticResource SuccessBrush}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Success"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource WhiteBrush}"/>
                    </Border>
                    <Border Background="{StaticResource WarningBrush}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Warning"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource BlackBrush}"/>
                    </Border>
                    <Border Background="{StaticResource ErrorBrush}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Error"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource WhiteBrush}"/>
                    </Border>
                    <Border Background="{StaticResource InfoBrush}"
                            Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                        <TextBlock Text="Info"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource WhiteBrush}"/>
                    </Border>
                </UniformGrid>

                <!-- 主题相关色彩 -->
                <TextBlock Text="主题相关色彩 (Theme Colors)"
                           Style="{StaticResource FluentTitleTextBlock}"
                           Margin="0,0,0,16"/>

                <Grid Margin="0,0,0,32">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 背景色彩 -->
                    <StackPanel Grid.Column="0" Margin="0,0,16,0">
                        <TextBlock Text="背景色彩"
                                   Style="{StaticResource FluentSubtitleTextBlock}"
                                   Margin="0,0,0,8"/>

                        <Border Background="{DynamicResource ThemeBackgroundPrimaryBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="{StaticResource BorderThicknessNormal}"
                                Height="60" Margin="0,4"
                                CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="主背景"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextPrimaryBrush}"/>
                        </Border>

                        <Border Background="{DynamicResource ThemeBackgroundSecondaryBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="{StaticResource BorderThicknessNormal}"
                                Height="60" Margin="0,4"
                                CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="次背景"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextPrimaryBrush}"/>
                        </Border>

                        <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="{StaticResource BorderThicknessNormal}"
                                Height="60" Margin="0,4"
                                CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="卡片背景"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextPrimaryBrush}"/>
                        </Border>
                    </StackPanel>

                    <!-- 文本色彩 -->
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="文本色彩"
                                   Style="{StaticResource FluentSubtitleTextBlock}"
                                   Margin="0,0,0,8"/>

                        <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="{StaticResource BorderThicknessNormal}"
                                Height="60" Margin="0,4"
                                CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="主要文本"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextPrimaryBrush}"/>
                        </Border>

                        <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="{StaticResource BorderThicknessNormal}"
                                Height="60" Margin="0,4"
                                CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="次要文本"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextSecondaryBrush}"/>
                        </Border>

                        <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="{StaticResource BorderThicknessNormal}"
                                Height="60" Margin="0,4"
                                CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="禁用文本"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextDisabledBrush}"/>
                        </Border>
                    </StackPanel>
                </Grid>

            </StackPanel>
        </ScrollViewer>

    </Grid>
</Window>
