﻿using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using FluentDesignWPF.Themes;

namespace FluentDesignWPF.Demo
{
    /// <summary>
    /// FluentDesignWPF 色彩系统演示主窗口
    /// 展示完整的色彩系统和主题切换功能
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            // 初始化主题系统
            InitializeTheme();

            // 订阅主题更改事件
            ThemeManager.ThemeChanged += OnThemeChanged;
        }

        /// <summary>
        /// 初始化主题系统
        /// </summary>
        private void InitializeTheme()
        {
            try
            {
                // 初始化为浅色主题
                ThemeManager.Initialize(Theme.Light);
                UpdateThemeButtonText();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"主题初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 主题切换按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 切换主题
                ThemeManager.ToggleTheme();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"主题切换失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 主题更改事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">主题更改事件参数</param>
        private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
        {
            // 更新按钮文本
            UpdateThemeButtonText();

            // 可以在这里添加其他主题更改后的处理逻辑
            Console.WriteLine($"主题已从 {e.OldTheme} 切换到 {e.NewTheme}");
        }

        /// <summary>
        /// 更新主题切换按钮的文本
        /// </summary>
        private void UpdateThemeButtonText()
        {
            if (ThemeToggleButton != null)
            {
                ThemeToggleButton.Content = ThemeManager.IsDarkTheme ? "切换到浅色主题" : "切换到深色主题";
            }
        }

        /// <summary>
        /// 窗口关闭时取消事件订阅
        /// </summary>
        /// <param name="e">取消事件参数</param>
        protected override void OnClosed(EventArgs e)
        {
            // 取消订阅主题更改事件
            ThemeManager.ThemeChanged -= OnThemeChanged;

            base.OnClosed(e);
        }
    }
}