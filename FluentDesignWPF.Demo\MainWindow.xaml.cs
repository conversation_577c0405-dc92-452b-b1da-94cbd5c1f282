using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using FluentDesignWPF.Themes;

namespace FluentDesignWPF.Demo
{
    /// <summary>
    /// FluentDesignWPF 色彩系统演示主窗口
    /// 展示完整的色彩系统和主题切换功能
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            // 简单初始化
            UpdateThemeButtonText();
        }



        /// <summary>
        /// 主题切换按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 简单的主题切换
                ThemeManager.ToggleTheme();
                UpdateThemeButtonText();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"主题切换失败: {ex.Message}\n\n详细信息: {ex}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// 更新主题切换按钮的文本
        /// </summary>
        private void UpdateThemeButtonText()
        {
            if (ThemeToggleButton != null)
            {
                ThemeToggleButton.Content = ThemeManager.IsDarkTheme ? "切换到浅色主题" : "切换到深色主题";
            }
        }


    }
}