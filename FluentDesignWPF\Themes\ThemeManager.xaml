<!--
    FluentSystemDesign WPF控件库 - 主题管理器资源字典
    
    此文件提供主题切换功能，支持深色和浅色主题的动态切换。
    通过重新定义通用画刷别名来实现主题切换效果。
    
    主题系统特点：
    1. 支持深色和浅色主题动态切换
    2. 使用DynamicResource实现实时主题更新
    3. 保持色彩系统的一致性
    4. 提供主题切换的编程接口
    
    使用方法：
    - 在应用程序中引用此资源字典
    - 通过ThemeManager类进行主题切换
    - 控件样式使用DynamicResource引用主题画刷
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引用基础资源字典 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
        <ResourceDictionary Source="Brushes.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 主题切换画刷 (Theme Brushes)      -->
    <!-- ================================ -->

    <!-- 这些画刷会根据当前主题动态更改，默认为浅色主题 -->

    <!-- 文本主题画刷 -->
    <SolidColorBrush x:Key="ThemeTextPrimaryBrush" Color="{StaticResource TextPrimaryLight}"/>
    <SolidColorBrush x:Key="ThemeTextSecondaryBrush" Color="{StaticResource TextSecondaryLight}"/>
    <SolidColorBrush x:Key="ThemeTextDisabledBrush" Color="{StaticResource TextDisabledLight}"/>
    <SolidColorBrush x:Key="ThemeTextHintBrush" Color="{StaticResource TextHintLight}"/>

    <!-- 背景主题画刷 -->
    <SolidColorBrush x:Key="ThemeBackgroundPrimaryBrush" Color="{StaticResource BackgroundPrimaryLight}"/>
    <SolidColorBrush x:Key="ThemeBackgroundSecondaryBrush" Color="{StaticResource BackgroundSecondaryLight}"/>
    <SolidColorBrush x:Key="ThemeBackgroundCardBrush" Color="{StaticResource BackgroundCardLight}"/>
    <SolidColorBrush x:Key="ThemeBackgroundHoverBrush" Color="{StaticResource BackgroundHoverLight}"/>
    <SolidColorBrush x:Key="ThemeBackgroundPressedBrush" Color="{StaticResource BackgroundPressedLight}"/>

    <!-- 边框主题画刷 -->
    <SolidColorBrush x:Key="ThemeBorderPrimaryBrush" Color="{StaticResource BorderPrimaryLight}"/>
    <SolidColorBrush x:Key="ThemeBorderSecondaryBrush" Color="{StaticResource BorderSecondaryLight}"/>
    <SolidColorBrush x:Key="ThemeBorderFocusBrush" Color="{StaticResource BorderFocusLight}"/>

    <!-- 分割线主题画刷 -->
    <SolidColorBrush x:Key="ThemeDividerBrush" Color="{StaticResource NeutralColor200}"/>

    <!-- 覆盖层主题画刷 -->
    <SolidColorBrush x:Key="ThemeOverlayBrush" Color="#80FFFFFF"/>

    <!-- ================================ -->
    <!-- 浅色主题资源 (Light Theme)        -->
    <!-- ================================ -->
    
    <!-- 浅色主题的资源字典，当切换到浅色主题时会应用这些资源 -->
    <ResourceDictionary x:Key="LightTheme">
        
        <!-- 文本色彩 -->
        <Color x:Key="ThemeTextPrimary">{StaticResource TextPrimaryLight}</Color>
        <Color x:Key="ThemeTextSecondary">{StaticResource TextSecondaryLight}</Color>
        <Color x:Key="ThemeTextDisabled">{StaticResource TextDisabledLight}</Color>
        <Color x:Key="ThemeTextHint">{StaticResource TextHintLight}</Color>

        <!-- 背景色彩 -->
        <Color x:Key="ThemeBackgroundPrimary">{StaticResource BackgroundPrimaryLight}</Color>
        <Color x:Key="ThemeBackgroundSecondary">{StaticResource BackgroundSecondaryLight}</Color>
        <Color x:Key="ThemeBackgroundCard">{StaticResource BackgroundCardLight}</Color>
        <Color x:Key="ThemeBackgroundHover">{StaticResource BackgroundHoverLight}</Color>
        <Color x:Key="ThemeBackgroundPressed">{StaticResource BackgroundPressedLight}</Color>

        <!-- 边框色彩 -->
        <Color x:Key="ThemeBorderPrimary">{StaticResource BorderPrimaryLight}</Color>
        <Color x:Key="ThemeBorderSecondary">{StaticResource BorderSecondaryLight}</Color>
        <Color x:Key="ThemeBorderFocus">{StaticResource BorderFocusLight}</Color>

        <!-- 特殊色彩 -->
        <Color x:Key="ThemeDivider">{StaticResource NeutralColor200}</Color>
        <Color x:Key="ThemeOverlay">#80FFFFFF</Color>

        <!-- 画刷资源 -->
        <SolidColorBrush x:Key="ThemeTextPrimaryBrush" Color="{StaticResource ThemeTextPrimary}"/>
        <SolidColorBrush x:Key="ThemeTextSecondaryBrush" Color="{StaticResource ThemeTextSecondary}"/>
        <SolidColorBrush x:Key="ThemeTextDisabledBrush" Color="{StaticResource ThemeTextDisabled}"/>
        <SolidColorBrush x:Key="ThemeTextHintBrush" Color="{StaticResource ThemeTextHint}"/>

        <SolidColorBrush x:Key="ThemeBackgroundPrimaryBrush" Color="{StaticResource ThemeBackgroundPrimary}"/>
        <SolidColorBrush x:Key="ThemeBackgroundSecondaryBrush" Color="{StaticResource ThemeBackgroundSecondary}"/>
        <SolidColorBrush x:Key="ThemeBackgroundCardBrush" Color="{StaticResource ThemeBackgroundCard}"/>
        <SolidColorBrush x:Key="ThemeBackgroundHoverBrush" Color="{StaticResource ThemeBackgroundHover}"/>
        <SolidColorBrush x:Key="ThemeBackgroundPressedBrush" Color="{StaticResource ThemeBackgroundPressed}"/>

        <SolidColorBrush x:Key="ThemeBorderPrimaryBrush" Color="{StaticResource ThemeBorderPrimary}"/>
        <SolidColorBrush x:Key="ThemeBorderSecondaryBrush" Color="{StaticResource ThemeBorderSecondary}"/>
        <SolidColorBrush x:Key="ThemeBorderFocusBrush" Color="{StaticResource ThemeBorderFocus}"/>

        <SolidColorBrush x:Key="ThemeDividerBrush" Color="{StaticResource ThemeDivider}"/>
        <SolidColorBrush x:Key="ThemeOverlayBrush" Color="{StaticResource ThemeOverlay}"/>

    </ResourceDictionary>

    <!-- ================================ -->
    <!-- 深色主题资源 (Dark Theme)         -->
    <!-- ================================ -->
    
    <!-- 深色主题的资源字典，当切换到深色主题时会应用这些资源 -->
    <ResourceDictionary x:Key="DarkTheme">
        
        <!-- 文本色彩 -->
        <Color x:Key="ThemeTextPrimary">{StaticResource TextPrimaryDark}</Color>
        <Color x:Key="ThemeTextSecondary">{StaticResource TextSecondaryDark}</Color>
        <Color x:Key="ThemeTextDisabled">{StaticResource TextDisabledDark}</Color>
        <Color x:Key="ThemeTextHint">{StaticResource TextHintDark}</Color>

        <!-- 背景色彩 -->
        <Color x:Key="ThemeBackgroundPrimary">{StaticResource BackgroundPrimaryDark}</Color>
        <Color x:Key="ThemeBackgroundSecondary">{StaticResource BackgroundSecondaryDark}</Color>
        <Color x:Key="ThemeBackgroundCard">{StaticResource BackgroundCardDark}</Color>
        <Color x:Key="ThemeBackgroundHover">{StaticResource BackgroundHoverDark}</Color>
        <Color x:Key="ThemeBackgroundPressed">{StaticResource BackgroundPressedDark}</Color>

        <!-- 边框色彩 -->
        <Color x:Key="ThemeBorderPrimary">{StaticResource BorderPrimaryDark}</Color>
        <Color x:Key="ThemeBorderSecondary">{StaticResource BorderSecondaryDark}</Color>
        <Color x:Key="ThemeBorderFocus">{StaticResource BorderFocusDark}</Color>

        <!-- 特殊色彩 -->
        <Color x:Key="ThemeDivider">{StaticResource NeutralColor700}</Color>
        <Color x:Key="ThemeOverlay">#80000000</Color>

        <!-- 画刷资源 -->
        <SolidColorBrush x:Key="ThemeTextPrimaryBrush" Color="{StaticResource ThemeTextPrimary}"/>
        <SolidColorBrush x:Key="ThemeTextSecondaryBrush" Color="{StaticResource ThemeTextSecondary}"/>
        <SolidColorBrush x:Key="ThemeTextDisabledBrush" Color="{StaticResource ThemeTextDisabled}"/>
        <SolidColorBrush x:Key="ThemeTextHintBrush" Color="{StaticResource ThemeTextHint}"/>

        <SolidColorBrush x:Key="ThemeBackgroundPrimaryBrush" Color="{StaticResource ThemeBackgroundPrimary}"/>
        <SolidColorBrush x:Key="ThemeBackgroundSecondaryBrush" Color="{StaticResource ThemeBackgroundSecondary}"/>
        <SolidColorBrush x:Key="ThemeBackgroundCardBrush" Color="{StaticResource ThemeBackgroundCard}"/>
        <SolidColorBrush x:Key="ThemeBackgroundHoverBrush" Color="{StaticResource ThemeBackgroundHover}"/>
        <SolidColorBrush x:Key="ThemeBackgroundPressedBrush" Color="{StaticResource ThemeBackgroundPressed}"/>

        <SolidColorBrush x:Key="ThemeBorderPrimaryBrush" Color="{StaticResource ThemeBorderPrimary}"/>
        <SolidColorBrush x:Key="ThemeBorderSecondaryBrush" Color="{StaticResource ThemeBorderSecondary}"/>
        <SolidColorBrush x:Key="ThemeBorderFocusBrush" Color="{StaticResource ThemeBorderFocus}"/>

        <SolidColorBrush x:Key="ThemeDividerBrush" Color="{StaticResource ThemeDivider}"/>
        <SolidColorBrush x:Key="ThemeOverlayBrush" Color="{StaticResource ThemeOverlay}"/>

    </ResourceDictionary>

</ResourceDictionary>
