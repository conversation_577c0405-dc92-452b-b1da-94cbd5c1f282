# FluentDesignWPF 控件库

一个基于 Microsoft Fluent Design System 的现代化 WPF 控件库，提供完整的色彩系统和主题切换功能。

## ✨ 特性

- 🎨 **完整的色彩系统** - 基于 Fluent Design System 的色彩规范
- 🌓 **主题切换** - 支持深色和浅色主题动态切换
- 🎯 **语义化色彩** - 成功、警告、错误、信息等语义色彩
- ♿ **无障碍支持** - 符合 WCAG 对比度标准
- 📱 **响应式设计** - 支持不同 DPI 和屏幕尺寸
- 🔧 **易于集成** - 简单的 XAML 引用即可使用

## 🚀 快速开始

### 安装

1. 克隆或下载此仓库
2. 在您的 WPF 项目中添加对 `FluentDesignWPF` 项目的引用

### 基本使用

1. **引用资源字典**

在您的 `App.xaml` 中添加：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/FluentDesignWPF;component/Themes/Generic.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

2. **初始化主题系统**

```csharp
using FluentDesignWPF.Themes;

public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        
        // 初始化主题系统
        ThemeManager.Initialize(Theme.Light);
    }
}
```

3. **使用色彩资源**

```xml
<Window Style="{StaticResource FluentWindow}">
    <Grid>
        <TextBlock Text="Hello FluentDesignWPF!" 
                   Style="{StaticResource FluentTitleTextBlock}"/>
        
        <Button Content="主要按钮" 
                Background="{StaticResource PrimaryBrush}"
                Foreground="{StaticResource WhiteBrush}"/>
        
        <Border Background="{StaticResource ThemeBackgroundCardBrush}"
                BorderBrush="{StaticResource ThemeBorderPrimaryBrush}"
                BorderThickness="1"
                CornerRadius="{StaticResource CornerRadiusNormal}">
            <TextBlock Text="卡片内容" 
                       Foreground="{StaticResource ThemeTextPrimaryBrush}"/>
        </Border>
    </Grid>
</Window>
```

## 🎨 色彩系统

### 主要色彩类别

- **主题色彩** - 蓝色系，用于主要操作和强调
- **语义色彩** - 成功(绿)、警告(黄)、错误(红)、信息(蓝)
- **中性色彩** - 灰色系，用于文本、边框、背景
- **主题相关色彩** - 根据当前主题动态变化

### 色彩层级

每种色彩提供 50-900 的完整层级：

```xml
<!-- 主题色彩示例 -->
<Border Background="{StaticResource PrimaryBrush50}"/>   <!-- 最浅 -->
<Border Background="{StaticResource PrimaryBrush500}"/>  <!-- 标准 -->
<Border Background="{StaticResource PrimaryBrush900}"/>  <!-- 最深 -->
```

### 主题切换

```csharp
// 切换主题
ThemeManager.ToggleTheme();

// 设置特定主题
ThemeManager.SetTheme(Theme.Dark);

// 监听主题变化
ThemeManager.ThemeChanged += (sender, e) =>
{
    Console.WriteLine($"主题切换: {e.OldTheme} -> {e.NewTheme}");
};
```

## 📁 项目结构

```
FluentDesignWPF/
├── Themes/                          # 主题和色彩系统
│   ├── Colors.xaml                  # 色彩定义
│   ├── Brushes.xaml                 # 画刷资源
│   ├── ThemeManager.xaml            # 主题管理器资源
│   ├── ThemeManager.cs              # 主题管理器类
│   ├── Generic.xaml                 # 通用资源字典
│   └── README.md                    # 色彩系统文档
├── Controls/                        # 控件库（未来扩展）
│   ├── Basic/                       # 基础控件
│   ├── Input/                       # 输入控件
│   ├── Navigation/                  # 导航控件
│   └── Layout/                      # 布局控件
└── FluentDesignWPF.csproj          # 项目文件

FluentDesignWPF.Demo/               # 示例应用
├── App.xaml                        # 应用程序入口
├── MainWindow.xaml                 # 主窗口
└── FluentDesignWPF.Demo.csproj     # 示例项目文件
```

## 🎯 示例应用

运行 `FluentDesignWPF.Demo` 项目查看完整的色彩系统演示：

```bash
cd FluentDesignWPF
dotnet run --project FluentDesignWPF.Demo
```

示例应用包含：
- 完整的色彩展示
- 主题切换功能
- 不同控件状态演示
- 响应式布局示例

## 🔧 开发环境

- **.NET 8.0** - 目标框架
- **WPF** - UI 框架
- **Visual Studio 2022** - 推荐 IDE

## 📚 文档

- [色彩系统详细文档](FluentDesignWPF/Themes/README.md)
- [控件开发指南](Docs/Controls/) (即将推出)
- [主题定制指南](Docs/Theming/) (即将推出)

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 此仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📋 开发规范

- 遵循 [FluentSystemDesign WPF控件库开发规则](DEVELOPMENT_RULES.md)
- 每个控件必须有独立的资源字典
- 支持深色和浅色主题
- 包含完整的 XML 文档注释
- 提供使用示例和文档

## 🎨 设计原则

1. **一致性** - 控件外观和行为保持一致
2. **响应性** - 支持不同屏幕尺寸和 DPI
3. **无障碍性** - 支持屏幕阅读器等辅助技术
4. **主题化** - 完整的深色/浅色主题支持
5. **流畅性** - 遵循 Fluent Design 的动效指南

## 📄 许可证

此项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Microsoft Fluent Design System](https://www.microsoft.com/design/fluent) - 设计指导
- [WPF UI](https://github.com/lepoco/wpfui) - 参考实现
- [Material Design](https://material.io/design) - 色彩理论参考

## 📞 联系

如有问题或建议，请创建 Issue 或联系维护者。

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
