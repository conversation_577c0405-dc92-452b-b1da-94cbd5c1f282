{"Version": 1, "WorkspaceRootPath": "D:\\Project\\04  FluentDesignWPF\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf\\themes\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|solutionrelative:fluentdesignwpf\\themes\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf\\themes\\brushes.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|solutionrelative:fluentdesignwpf\\themes\\brushes.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Colors.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Colors.xaml", "RelativeDocumentMoniker": "FluentDesignWPF\\Themes\\Colors.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Colors.xaml", "RelativeToolTip": "FluentDesignWPF\\Themes\\Colors.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T22:28:34.969Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Brushes.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Brushes.xaml", "RelativeDocumentMoniker": "FluentDesignWPF\\Themes\\Brushes.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Brushes.xaml", "RelativeToolTip": "FluentDesignWPF\\Themes\\Brushes.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T22:28:23.796Z", "EditorCaption": ""}]}]}]}