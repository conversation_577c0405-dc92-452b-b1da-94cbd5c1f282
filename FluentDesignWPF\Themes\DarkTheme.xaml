<!--
    FluentSystemDesign WPF控件库 - 深色主题资源字典
    
    此文件定义了深色主题下的所有主题相关色彩和画刷。
    当切换到深色主题时，会使用此资源字典替换主题相关的资源。
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引用基础色彩定义 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 深色主题色彩 (Dark Theme Colors)  -->
    <!-- ================================ -->
    
    <!-- 文本色彩 -->
    <Color x:Key="ThemeTextPrimary">{StaticResource TextPrimaryDark}</Color>
    <Color x:Key="ThemeTextSecondary">{StaticResource TextSecondaryDark}</Color>
    <Color x:Key="ThemeTextDisabled">{StaticResource TextDisabledDark}</Color>
    <Color x:Key="ThemeTextHint">{StaticResource TextHintDark}</Color>

    <!-- 背景色彩 -->
    <Color x:Key="ThemeBackgroundPrimary">{StaticResource BackgroundPrimaryDark}</Color>
    <Color x:Key="ThemeBackgroundSecondary">{StaticResource BackgroundSecondaryDark}</Color>
    <Color x:Key="ThemeBackgroundCard">{StaticResource BackgroundCardDark}</Color>
    <Color x:Key="ThemeBackgroundHover">{StaticResource BackgroundHoverDark}</Color>
    <Color x:Key="ThemeBackgroundPressed">{StaticResource BackgroundPressedDark}</Color>

    <!-- 边框色彩 -->
    <Color x:Key="ThemeBorderPrimary">{StaticResource BorderPrimaryDark}</Color>
    <Color x:Key="ThemeBorderSecondary">{StaticResource BorderSecondaryDark}</Color>
    <Color x:Key="ThemeBorderFocus">{StaticResource BorderFocusDark}</Color>

    <!-- 特殊色彩 -->
    <Color x:Key="ThemeDivider">{StaticResource NeutralColor700}</Color>
    <Color x:Key="ThemeOverlay">#80000000</Color>

    <!-- ================================ -->
    <!-- 深色主题画刷 (Dark Theme Brushes) -->
    <!-- ================================ -->
    
    <!-- 文本画刷 -->
    <SolidColorBrush x:Key="ThemeTextPrimaryBrush" Color="{StaticResource ThemeTextPrimary}"/>
    <SolidColorBrush x:Key="ThemeTextSecondaryBrush" Color="{StaticResource ThemeTextSecondary}"/>
    <SolidColorBrush x:Key="ThemeTextDisabledBrush" Color="{StaticResource ThemeTextDisabled}"/>
    <SolidColorBrush x:Key="ThemeTextHintBrush" Color="{StaticResource ThemeTextHint}"/>

    <!-- 背景画刷 -->
    <SolidColorBrush x:Key="ThemeBackgroundPrimaryBrush" Color="{StaticResource ThemeBackgroundPrimary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundSecondaryBrush" Color="{StaticResource ThemeBackgroundSecondary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundCardBrush" Color="{StaticResource ThemeBackgroundCard}"/>
    <SolidColorBrush x:Key="ThemeBackgroundHoverBrush" Color="{StaticResource ThemeBackgroundHover}"/>
    <SolidColorBrush x:Key="ThemeBackgroundPressedBrush" Color="{StaticResource ThemeBackgroundPressed}"/>

    <!-- 边框画刷 -->
    <SolidColorBrush x:Key="ThemeBorderPrimaryBrush" Color="{StaticResource ThemeBorderPrimary}"/>
    <SolidColorBrush x:Key="ThemeBorderSecondaryBrush" Color="{StaticResource ThemeBorderSecondary}"/>
    <SolidColorBrush x:Key="ThemeBorderFocusBrush" Color="{StaticResource ThemeBorderFocus}"/>

    <!-- 特殊画刷 -->
    <SolidColorBrush x:Key="ThemeDividerBrush" Color="{StaticResource ThemeDivider}"/>
    <SolidColorBrush x:Key="ThemeOverlayBrush" Color="{StaticResource ThemeOverlay}"/>

    <!-- ================================ -->
    <!-- 控件状态画刷 (Control State Brushes) -->
    <!-- ================================ -->
    
    <!-- 输入控件状态画刷 -->
    <SolidColorBrush x:Key="InputNormalBrush" Color="{StaticResource BackgroundPrimaryDark}"/>
    <SolidColorBrush x:Key="InputHoverBrush" Color="{StaticResource BackgroundHoverDark}"/>
    <SolidColorBrush x:Key="InputFocusBrush" Color="{StaticResource BackgroundPrimaryDark}"/>
    <SolidColorBrush x:Key="InputDisabledBrush" Color="{StaticResource BackgroundSecondaryDark}"/>

</ResourceDictionary>
