using System;
using System.Windows;
using System.Windows.Media;

namespace FluentDesignWPF.Themes
{
    /// <summary>
    /// 主题管理器，用于管理应用程序的主题切换
    /// 支持深色和浅色主题的动态切换
    /// </summary>
    public static class ThemeManager
    {
        #region 事件

        /// <summary>
        /// 主题更改事件
        /// </summary>
        public static event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        #endregion

        #region 属性

        private static Theme _currentTheme = Theme.Light;

        /// <summary>
        /// 获取当前主题
        /// </summary>
        public static Theme CurrentTheme
        {
            get => _currentTheme;
            private set
            {
                if (_currentTheme != value)
                {
                    var oldTheme = _currentTheme;
                    _currentTheme = value;
                    OnThemeChanged(new ThemeChangedEventArgs(oldTheme, value));
                }
            }
        }

        /// <summary>
        /// 获取当前是否为深色主题
        /// </summary>
        public static bool IsDarkTheme => CurrentTheme == Theme.Dark;

        /// <summary>
        /// 获取当前是否为浅色主题
        /// </summary>
        public static bool IsLightTheme => CurrentTheme == Theme.Light;

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置应用程序主题
        /// </summary>
        /// <param name="theme">要设置的主题</param>
        public static void SetTheme(Theme theme)
        {
            if (Application.Current?.Resources == null)
            {
                throw new InvalidOperationException("Application.Current.Resources is null. Make sure this method is called after Application initialization.");
            }

            CurrentTheme = theme;
            ApplyTheme(theme);
        }

        /// <summary>
        /// 切换主题（在深色和浅色之间切换）
        /// </summary>
        public static void ToggleTheme()
        {
            SetTheme(CurrentTheme == Theme.Light ? Theme.Dark : Theme.Light);
        }

        /// <summary>
        /// 初始化主题系统
        /// </summary>
        /// <param name="initialTheme">初始主题，默认为浅色主题</param>
        public static void Initialize(Theme initialTheme = Theme.Light)
        {
            if (Application.Current?.Resources == null)
            {
                throw new InvalidOperationException("Application.Current.Resources is null. Make sure this method is called after Application initialization.");
            }

            // 确保主题资源字典已加载
            EnsureThemeResourcesLoaded();

            // 设置初始主题
            SetTheme(initialTheme);
        }

        /// <summary>
        /// 获取主题色彩
        /// </summary>
        /// <param name="colorKey">色彩键名</param>
        /// <returns>色彩值，如果未找到则返回透明色</returns>
        public static Color GetThemeColor(string colorKey)
        {
            if (Application.Current?.Resources == null)
                return Colors.Transparent;

            if (Application.Current.Resources.Contains(colorKey) && 
                Application.Current.Resources[colorKey] is Color color)
            {
                return color;
            }

            return Colors.Transparent;
        }

        /// <summary>
        /// 获取主题画刷
        /// </summary>
        /// <param name="brushKey">画刷键名</param>
        /// <returns>画刷对象，如果未找到则返回透明画刷</returns>
        public static Brush GetThemeBrush(string brushKey)
        {
            if (Application.Current?.Resources == null)
                return Brushes.Transparent;

            if (Application.Current.Resources.Contains(brushKey) && 
                Application.Current.Resources[brushKey] is Brush brush)
            {
                return brush;
            }

            return Brushes.Transparent;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 应用指定主题
        /// </summary>
        /// <param name="theme">要应用的主题</param>
        private static void ApplyTheme(Theme theme)
        {
            var resources = Application.Current.Resources;

            // 直接更新主题画刷的颜色
            if (theme == Theme.Dark)
            {
                // 应用深色主题
                UpdateBrushColor(resources, "ThemeTextPrimaryBrush", "TextPrimaryDark");
                UpdateBrushColor(resources, "ThemeTextSecondaryBrush", "TextSecondaryDark");
                UpdateBrushColor(resources, "ThemeTextDisabledBrush", "TextDisabledDark");
                UpdateBrushColor(resources, "ThemeTextHintBrush", "TextHintDark");

                UpdateBrushColor(resources, "ThemeBackgroundPrimaryBrush", "BackgroundPrimaryDark");
                UpdateBrushColor(resources, "ThemeBackgroundSecondaryBrush", "BackgroundSecondaryDark");
                UpdateBrushColor(resources, "ThemeBackgroundCardBrush", "BackgroundCardDark");
                UpdateBrushColor(resources, "ThemeBackgroundHoverBrush", "BackgroundHoverDark");
                UpdateBrushColor(resources, "ThemeBackgroundPressedBrush", "BackgroundPressedDark");

                UpdateBrushColor(resources, "ThemeBorderPrimaryBrush", "BorderPrimaryDark");
                UpdateBrushColor(resources, "ThemeBorderSecondaryBrush", "BorderSecondaryDark");
                UpdateBrushColor(resources, "ThemeBorderFocusBrush", "BorderFocusDark");

                UpdateBrushColor(resources, "ThemeDividerBrush", "NeutralColor700");
                resources["ThemeOverlayBrush"] = new SolidColorBrush(Color.FromArgb(128, 0, 0, 0));
            }
            else
            {
                // 应用浅色主题
                UpdateBrushColor(resources, "ThemeTextPrimaryBrush", "TextPrimaryLight");
                UpdateBrushColor(resources, "ThemeTextSecondaryBrush", "TextSecondaryLight");
                UpdateBrushColor(resources, "ThemeTextDisabledBrush", "TextDisabledLight");
                UpdateBrushColor(resources, "ThemeTextHintBrush", "TextHintLight");

                UpdateBrushColor(resources, "ThemeBackgroundPrimaryBrush", "BackgroundPrimaryLight");
                UpdateBrushColor(resources, "ThemeBackgroundSecondaryBrush", "BackgroundSecondaryLight");
                UpdateBrushColor(resources, "ThemeBackgroundCardBrush", "BackgroundCardLight");
                UpdateBrushColor(resources, "ThemeBackgroundHoverBrush", "BackgroundHoverLight");
                UpdateBrushColor(resources, "ThemeBackgroundPressedBrush", "BackgroundPressedLight");

                UpdateBrushColor(resources, "ThemeBorderPrimaryBrush", "BorderPrimaryLight");
                UpdateBrushColor(resources, "ThemeBorderSecondaryBrush", "BorderSecondaryLight");
                UpdateBrushColor(resources, "ThemeBorderFocusBrush", "BorderFocusLight");

                UpdateBrushColor(resources, "ThemeDividerBrush", "NeutralColor200");
                resources["ThemeOverlayBrush"] = new SolidColorBrush(Color.FromArgb(128, 255, 255, 255));
            }
        }

        /// <summary>
        /// 更新画刷颜色
        /// </summary>
        /// <param name="resources">资源字典</param>
        /// <param name="brushKey">画刷键名</param>
        /// <param name="colorKey">颜色键名</param>
        private static void UpdateBrushColor(ResourceDictionary resources, string brushKey, string colorKey)
        {
            if (resources.Contains(colorKey) && resources[colorKey] is Color color)
            {
                if (resources.Contains(brushKey) && resources[brushKey] is SolidColorBrush brush)
                {
                    brush.Color = color;
                }
                else
                {
                    resources[brushKey] = new SolidColorBrush(color);
                }
            }
        }

        /// <summary>
        /// 确保主题资源字典已加载
        /// </summary>
        private static void EnsureThemeResourcesLoaded()
        {
            var resources = Application.Current.Resources;
            
            // 检查是否已加载主题管理器资源
            var themeManagerUri = new Uri("pack://application:,,,/FluentDesignWPF;component/Themes/ThemeManager.xaml");
            
            bool hasThemeManager = false;
            foreach (var mergedDict in resources.MergedDictionaries)
            {
                if (mergedDict.Source == themeManagerUri)
                {
                    hasThemeManager = true;
                    break;
                }
            }

            if (!hasThemeManager)
            {
                var themeManagerDict = new ResourceDictionary { Source = themeManagerUri };
                resources.MergedDictionaries.Add(themeManagerDict);
            }
        }

        /// <summary>
        /// 触发主题更改事件
        /// </summary>
        /// <param name="e">事件参数</param>
        private static void OnThemeChanged(ThemeChangedEventArgs e)
        {
            ThemeChanged?.Invoke(null, e);
        }

        #endregion
    }

    /// <summary>
    /// 主题枚举
    /// </summary>
    public enum Theme
    {
        /// <summary>
        /// 浅色主题
        /// </summary>
        Light,

        /// <summary>
        /// 深色主题
        /// </summary>
        Dark
    }

    /// <summary>
    /// 主题更改事件参数
    /// </summary>
    public class ThemeChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 初始化主题更改事件参数
        /// </summary>
        /// <param name="oldTheme">旧主题</param>
        /// <param name="newTheme">新主题</param>
        public ThemeChangedEventArgs(Theme oldTheme, Theme newTheme)
        {
            OldTheme = oldTheme;
            NewTheme = newTheme;
        }

        /// <summary>
        /// 获取旧主题
        /// </summary>
        public Theme OldTheme { get; }

        /// <summary>
        /// 获取新主题
        /// </summary>
        public Theme NewTheme { get; }
    }
}
