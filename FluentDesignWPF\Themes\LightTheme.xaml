<!--
    FluentSystemDesign WPF控件库 - 浅色主题资源字典
    
    此文件定义了浅色主题下的所有主题相关色彩和画刷。
    当切换到浅色主题时，会使用此资源字典替换主题相关的资源。
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引用基础色彩定义 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 浅色主题色彩 (Light Theme Colors) -->
    <!-- ================================ -->
    
    <!-- 文本色彩 -->
    <Color x:Key="ThemeTextPrimary">{StaticResource TextPrimaryLight}</Color>
    <Color x:Key="ThemeTextSecondary">{StaticResource TextSecondaryLight}</Color>
    <Color x:Key="ThemeTextDisabled">{StaticResource TextDisabledLight}</Color>
    <Color x:Key="ThemeTextHint">{StaticResource TextHintLight}</Color>

    <!-- 背景色彩 -->
    <Color x:Key="ThemeBackgroundPrimary">{StaticResource BackgroundPrimaryLight}</Color>
    <Color x:Key="ThemeBackgroundSecondary">{StaticResource BackgroundSecondaryLight}</Color>
    <Color x:Key="ThemeBackgroundCard">{StaticResource BackgroundCardLight}</Color>
    <Color x:Key="ThemeBackgroundHover">{StaticResource BackgroundHoverLight}</Color>
    <Color x:Key="ThemeBackgroundPressed">{StaticResource BackgroundPressedLight}</Color>

    <!-- 边框色彩 -->
    <Color x:Key="ThemeBorderPrimary">{StaticResource BorderPrimaryLight}</Color>
    <Color x:Key="ThemeBorderSecondary">{StaticResource BorderSecondaryLight}</Color>
    <Color x:Key="ThemeBorderFocus">{StaticResource BorderFocusLight}</Color>

    <!-- 特殊色彩 -->
    <Color x:Key="ThemeDivider">{StaticResource NeutralColor200}</Color>
    <Color x:Key="ThemeOverlay">#80FFFFFF</Color>

    <!-- ================================ -->
    <!-- 浅色主题画刷 (Light Theme Brushes) -->
    <!-- ================================ -->
    
    <!-- 文本画刷 -->
    <SolidColorBrush x:Key="ThemeTextPrimaryBrush" Color="{StaticResource ThemeTextPrimary}"/>
    <SolidColorBrush x:Key="ThemeTextSecondaryBrush" Color="{StaticResource ThemeTextSecondary}"/>
    <SolidColorBrush x:Key="ThemeTextDisabledBrush" Color="{StaticResource ThemeTextDisabled}"/>
    <SolidColorBrush x:Key="ThemeTextHintBrush" Color="{StaticResource ThemeTextHint}"/>

    <!-- 背景画刷 -->
    <SolidColorBrush x:Key="ThemeBackgroundPrimaryBrush" Color="{StaticResource ThemeBackgroundPrimary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundSecondaryBrush" Color="{StaticResource ThemeBackgroundSecondary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundCardBrush" Color="{StaticResource ThemeBackgroundCard}"/>
    <SolidColorBrush x:Key="ThemeBackgroundHoverBrush" Color="{StaticResource ThemeBackgroundHover}"/>
    <SolidColorBrush x:Key="ThemeBackgroundPressedBrush" Color="{StaticResource ThemeBackgroundPressed}"/>

    <!-- 边框画刷 -->
    <SolidColorBrush x:Key="ThemeBorderPrimaryBrush" Color="{StaticResource ThemeBorderPrimary}"/>
    <SolidColorBrush x:Key="ThemeBorderSecondaryBrush" Color="{StaticResource ThemeBorderSecondary}"/>
    <SolidColorBrush x:Key="ThemeBorderFocusBrush" Color="{StaticResource ThemeBorderFocus}"/>

    <!-- 特殊画刷 -->
    <SolidColorBrush x:Key="ThemeDividerBrush" Color="{StaticResource ThemeDivider}"/>
    <SolidColorBrush x:Key="ThemeOverlayBrush" Color="{StaticResource ThemeOverlay}"/>

    <!-- ================================ -->
    <!-- 控件状态画刷 (Control State Brushes) -->
    <!-- ================================ -->
    
    <!-- 输入控件状态画刷 -->
    <SolidColorBrush x:Key="InputNormalBrush" Color="{StaticResource BackgroundPrimaryLight}"/>
    <SolidColorBrush x:Key="InputHoverBrush" Color="{StaticResource BackgroundHoverLight}"/>
    <SolidColorBrush x:Key="InputFocusBrush" Color="{StaticResource BackgroundPrimaryLight}"/>
    <SolidColorBrush x:Key="InputDisabledBrush" Color="{StaticResource BackgroundSecondaryLight}"/>

</ResourceDictionary>
